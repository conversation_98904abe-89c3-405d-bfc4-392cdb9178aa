"""Tests for common.settings module."""

import os
from unittest.mock import Mock, patch

import pytest
from pydantic import ValidationError

from common.settings import (
    AppSettings,
    DatabaseSettings,
    LlmSettings,
    LlmGatewaySettings,
    OTLPSettings,
    CorsSettings,
    TaskProcesssorSettings,
    settings
)


class TestDatabaseSettings:
    """Test DatabaseSettings class."""

    def test_default_values(self):
        """Test default database settings values."""
        db_settings = DatabaseSettings()
        
        assert db_settings.connection_string == "postgresql://postgres:postgres@localhost:5432/postgres"

    def test_custom_values(self):
        """Test custom database settings values."""
        custom_connection = "**********************************/mydb"
        db_settings = DatabaseSettings(connection_string=custom_connection)
        
        assert db_settings.connection_string == custom_connection


class TestLlmSettings:
    """Test LlmSettings class."""

    def test_default_values(self):
        """Test default LLM settings values."""
        llm_settings = LlmSettings()
        
        assert llm_settings.base_url == "http://localhost:4000"
        assert llm_settings.api_key == "test"

    def test_custom_values(self):
        """Test custom LLM settings values."""
        llm_settings = LlmSettings(
            base_url="https://api.openai.com",
            api_key="sk-custom-key"
        )
        
        assert llm_settings.base_url == "https://api.openai.com"
        assert llm_settings.api_key == "sk-custom-key"


class TestLlmGatewaySettings:
    """Test LlmGatewaySettings class."""

    def test_default_values(self):
        """Test default LLM gateway settings values."""
        gateway_settings = LlmGatewaySettings()
        
        assert gateway_settings.base_url == "http://localhost:4000"
        assert gateway_settings.api_key == "test"

    def test_custom_values(self):
        """Test custom LLM gateway settings values."""
        gateway_settings = LlmGatewaySettings(
            base_url="https://gateway.example.com",
            api_key="gateway-key-123"
        )
        
        assert gateway_settings.base_url == "https://gateway.example.com"
        assert gateway_settings.api_key == "gateway-key-123"


class TestOTLPSettings:
    """Test OTLPSettings class."""

    def test_default_values(self):
        """Test default OTLP settings values."""
        otlp_settings = OTLPSettings()
        
        assert otlp_settings.endpoint == "http://localhost:4317"

    def test_custom_values(self):
        """Test custom OTLP settings values."""
        otlp_settings = OTLPSettings(endpoint="https://otlp.example.com:4317")
        
        assert otlp_settings.endpoint == "https://otlp.example.com:4317"


class TestCorsSettings:
    """Test CorsSettings class."""

    def test_default_values(self):
        """Test default CORS settings values."""
        cors_settings = CorsSettings()
        
        assert cors_settings.allow_origins == ["*"]
        assert cors_settings.allow_credentials is True
        assert cors_settings.allow_methods == ["*"]
        assert cors_settings.allow_headers == ["*"]

    def test_custom_values(self):
        """Test custom CORS settings values."""
        cors_settings = CorsSettings(
            allow_origins=["https://example.com", "https://app.example.com"],
            allow_credentials=False,
            allow_methods=["GET", "POST"],
            allow_headers=["Content-Type", "Authorization"]
        )
        
        assert cors_settings.allow_origins == ["https://example.com", "https://app.example.com"]
        assert cors_settings.allow_credentials is False
        assert cors_settings.allow_methods == ["GET", "POST"]
        assert cors_settings.allow_headers == ["Content-Type", "Authorization"]


class TestTaskProcessorSettings:
    """Test TaskProcesssorSettings class."""

    def test_default_values(self):
        """Test default task processor settings values."""
        task_settings = TaskProcesssorSettings()
        
        assert task_settings.task_queue_namespace == "encore-task-processor"
        assert task_settings.task_max_retries == 3
        assert task_settings.task_time_limit_ms == 180000
        assert task_settings.task_min_backoff_ms == 180000
        assert task_settings.task_max_backoff_ms == 3600000
        assert task_settings.task_visibility_timeout == 600
        assert task_settings.polling_interval_seconds == 5
        assert task_settings.batch_size == 10
        assert task_settings.bucket_name == "aca-dev-encore-us-east-1"
        assert task_settings.callback_timeout == 10
        assert task_settings.store_job_params is False

    def test_custom_values(self):
        """Test custom task processor settings values."""
        task_settings = TaskProcesssorSettings(
            task_queue_namespace="custom-namespace",
            task_max_retries=5,
            batch_size=20,
            bucket_name="custom-bucket",
            store_job_params=True
        )
        
        assert task_settings.task_queue_namespace == "custom-namespace"
        assert task_settings.task_max_retries == 5
        assert task_settings.batch_size == 20
        assert task_settings.bucket_name == "custom-bucket"
        assert task_settings.store_job_params is True


class TestAppSettings:
    """Test AppSettings class."""

    def test_default_initialization(self):
        """Test AppSettings initialization with defaults."""
        app_settings = AppSettings()
        
        assert isinstance(app_settings.database, DatabaseSettings)
        assert isinstance(app_settings.llm, LlmSettings)
        assert isinstance(app_settings.llm_gateway, LlmGatewaySettings)
        assert isinstance(app_settings.otlp, OTLPSettings)
        assert isinstance(app_settings.cors, CorsSettings)
        assert isinstance(app_settings.task_processor, TaskProcesssorSettings)

    @patch.dict(os.environ, {"is_local": "true"})
    def test_get_settings_local(self):
        """Test get_settings for local environment."""
        with patch.object(AppSettings, '__init__', return_value=None) as mock_init:
            mock_instance = Mock()
            mock_init.return_value = mock_instance
            
            result = AppSettings.get_settings()
            
            mock_init.assert_called_once()

    @patch.dict(os.environ, {"env_name": "production"}, clear=True)
    @patch.object(AppSettings, 'from_aws_parameter_store')
    def test_get_settings_aws(self, mock_from_aws):
        """Test get_settings for AWS environment."""
        mock_instance = Mock()
        mock_from_aws.return_value = mock_instance
        
        result = AppSettings.get_settings()
        
        mock_from_aws.assert_called_once_with("production")
        assert result == mock_instance

    @patch('common.settings.boto3.client')
    def test_fetch_params_string_value(self, mock_boto_client):
        """Test _fetch_params with string parameter."""
        mock_ssm = Mock()
        mock_ssm.get_parameter.return_value = {
            "Parameter": {"Value": "test-value"}
        }
        mock_boto_client.return_value = mock_ssm
        
        result = AppSettings._fetch_params(mock_ssm, "/test/parameter")
        
        assert result == "test-value"
        mock_ssm.get_parameter.assert_called_once_with(
            Name="/test/parameter",
            WithDecryption=True
        )

    @patch('common.settings.boto3.client')
    def test_fetch_params_dict_value(self, mock_boto_client):
        """Test _fetch_params with dictionary parameter."""
        mock_ssm = Mock()
        mock_ssm.get_parameter.side_effect = [
            {"Parameter": {"Value": "value1"}},
            {"Parameter": {"Value": "value2"}}
        ]
        mock_boto_client.return_value = mock_ssm
        
        param_dict = {
            "key1": "/test/param1",
            "key2": "/test/param2"
        }
        
        result = AppSettings._fetch_params(mock_ssm, param_dict)
        
        expected = {"key1": "value1", "key2": "value2"}
        assert result == expected

    def test_settings_singleton(self):
        """Test that settings is properly initialized."""
        assert isinstance(settings, AppSettings)
