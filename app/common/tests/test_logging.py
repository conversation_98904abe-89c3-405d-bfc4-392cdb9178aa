"""Tests for common.logging module."""

import logging
import os
from unittest.mock import patch, Mock

import pytest

from common.logging import LoggingSetup, FORMAT


class TestLoggingSetup:
    """Test LoggingSetup class."""

    def test_setup_logger_returns_logger(self):
        """Test setup_logger returns a logger instance."""
        logger = LoggingSetup.setup_logger("test_logger")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_logger"

    def test_setup_logger_different_names(self):
        """Test setup_logger with different names."""
        logger1 = LoggingSetup.setup_logger("logger1")
        logger2 = LoggingSetup.setup_logger("logger2")
        
        assert logger1.name == "logger1"
        assert logger2.name == "logger2"
        assert logger1 != logger2

    def test_setup_logger_same_name_returns_same_instance(self):
        """Test setup_logger with same name returns same logger instance."""
        logger1 = LoggingSetup.setup_logger("same_logger")
        logger2 = LoggingSetup.setup_logger("same_logger")
        
        assert logger1 is logger2

    def test_setup_logger_with_module_name(self):
        """Test setup_logger with __name__ pattern."""
        module_name = "test.module.name"
        logger = LoggingSetup.setup_logger(module_name)
        
        assert logger.name == module_name

    def test_logger_has_correct_level_dev(self):
        """Test logger level in dev environment."""
        with patch.dict(os.environ, {"env_name": "dev"}):
            # Need to reload the module to pick up env change
            import importlib
            import common.logging
            importlib.reload(common.logging)
            
            logger = common.logging.LoggingSetup.setup_logger("test_dev")
            
            # Check that root logger is configured for DEBUG
            root_logger = logging.getLogger()
            assert root_logger.level == logging.DEBUG

    def test_logger_has_correct_level_production(self):
        """Test logger level in production environment."""
        with patch.dict(os.environ, {"env_name": "production"}):
            # Need to reload the module to pick up env change
            import importlib
            import common.logging
            importlib.reload(common.logging)
            
            logger = common.logging.LoggingSetup.setup_logger("test_prod")
            
            # Check that root logger is configured for INFO
            root_logger = logging.getLogger()
            assert root_logger.level == logging.INFO

    def test_logger_format_constant(self):
        """Test FORMAT constant has correct value."""
        expected_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        assert FORMAT == expected_format

    @patch('common.logging.logging.basicConfig')
    def test_logging_basic_config_called_dev(self, mock_basic_config):
        """Test logging.basicConfig called with correct params for dev."""
        with patch.dict(os.environ, {"env_name": "dev"}):
            # Reload module to trigger basicConfig call
            import importlib
            import common.logging
            importlib.reload(common.logging)
            
            mock_basic_config.assert_called_with(
                level=logging.DEBUG,
                format=FORMAT
            )

    @patch('common.logging.logging.basicConfig')
    def test_logging_basic_config_called_non_dev(self, mock_basic_config):
        """Test logging.basicConfig called with correct params for non-dev."""
        with patch.dict(os.environ, {"env_name": "production"}):
            # Reload module to trigger basicConfig call
            import importlib
            import common.logging
            importlib.reload(common.logging)
            
            mock_basic_config.assert_called_with(
                level=logging.INFO,
                format=FORMAT
            )

    def test_logger_can_log_messages(self):
        """Test that logger can log messages at different levels."""
        logger = LoggingSetup.setup_logger("test_logging")
        
        # These should not raise exceptions
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")

    def test_logger_with_empty_name(self):
        """Test setup_logger with empty name."""
        logger = LoggingSetup.setup_logger("")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == ""

    def test_logger_with_none_name(self):
        """Test setup_logger with None name."""
        # This should work as logging.getLogger(None) returns root logger
        logger = LoggingSetup.setup_logger(None)
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "root"

    @patch.dict(os.environ, {}, clear=True)
    def test_logger_default_env_behavior(self):
        """Test logger behavior when env_name is not set."""
        # When env_name is not set, should default to INFO level
        import importlib
        import common.logging
        importlib.reload(common.logging)
        
        root_logger = logging.getLogger()
        assert root_logger.level == logging.INFO

    def test_setup_logger_is_static_method(self):
        """Test that setup_logger is accessible as static method."""
        # Should be able to call without instantiating LoggingSetup
        logger = LoggingSetup.setup_logger("static_test")
        assert isinstance(logger, logging.Logger)
        
        # Should also work by calling on class directly
        logger2 = LoggingSetup.setup_logger("static_test2")
        assert isinstance(logger2, logging.Logger)
