"""Tests for common.s3_storage module."""

import json
import uuid
from unittest.mock import Mock, patch, MagicMock

import pytest
from botocore.exceptions import ClientError

from common.s3_storage import S3Storage, get_s3_storage


class TestS3Storage:
    """Test S3Storage class."""

    @patch('common.s3_storage.boto3.client')
    @patch('common.s3_storage.settings')
    def test_init_default_values(self, mock_settings, mock_boto_client):
        """Test S3Storage initialization with default values."""
        mock_settings.task_processor.bucket_name = "test-bucket"
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        storage = S3Storage()

        assert storage.bucket_name == "test-bucket"
        assert storage.prefix == "batch-jobs"
        assert storage.s3_client == mock_client

    @patch('common.s3_storage.boto3.client')
    def test_init_custom_values(self, mock_boto_client):
        """Test S3Storage initialization with custom values."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        storage = S3Storage(bucket_name="custom-bucket", prefix="custom/prefix/")

        assert storage.bucket_name == "custom-bucket"
        assert storage.prefix == "custom/prefix"  # Should strip trailing slash

    @patch('common.s3_storage.boto3.client')
    def test_store_result_success(self, mock_boto_client):
        """Test successful result storage."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket", prefix="test-prefix")

        test_data = {"result": "success", "value": 123}
        item_id = "test-item-123"

        result = storage.store_result(item_id, test_data)

        expected_key = f"test-prefix/{item_id}.json"
        mock_client.put_object.assert_called_once_with(
            Bucket="test-bucket",
            Key=expected_key,
            Body=json.dumps(test_data),
            ContentType="application/json"
        )
        assert result == expected_key

    @patch('common.s3_storage.boto3.client')
    def test_store_result_client_error(self, mock_boto_client):
        """Test result storage with client error."""
        mock_client = Mock()
        mock_client.put_object.side_effect = ClientError(
            {"Error": {"Code": "AccessDenied", "Message": "Access denied"}},
            "PutObject"
        )
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket")

        result = storage.store_result("test-item", {"data": "test"})

        assert result is None

    @patch('common.s3_storage.boto3.client')
    def test_get_result_success(self, mock_boto_client):
        """Test successful result retrieval."""
        mock_client = Mock()
        test_data = {"retrieved": "data", "count": 456}
        mock_response = {
            "Body": Mock()
        }
        mock_response["Body"].read.return_value = json.dumps(test_data).encode()
        mock_client.get_object.return_value = mock_response
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket", prefix="test-prefix")

        result = storage.get_result("test-item-456")

        expected_key = f"test-prefix/test-item-456.json"
        mock_client.get_object.assert_called_once_with(
            Bucket="test-bucket",
            Key=expected_key
        )
        assert result == test_data

    @patch('common.s3_storage.boto3.client')
    def test_get_result_not_found(self, mock_boto_client):
        """Test result retrieval when object not found."""
        mock_client = Mock()
        mock_client.get_object.side_effect = ClientError(
            {"Error": {"Code": "NoSuchKey", "Message": "Key not found"}},
            "GetObject"
        )
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket")

        result = storage.get_result("nonexistent-item")

        assert result is None

    @patch('common.s3_storage.boto3.client')
    def test_get_result_invalid_json(self, mock_boto_client):
        """Test result retrieval with invalid JSON."""
        mock_client = Mock()
        mock_response = {
            "Body": Mock()
        }
        mock_response["Body"].read.return_value = b"invalid json content"
        mock_client.get_object.return_value = mock_response
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket")

        result = storage.get_result("test-item")

        assert result is None

    @patch('common.s3_storage.boto3.client')
    def test_get_presigned_url_success(self, mock_boto_client):
        """Test successful presigned URL generation."""
        mock_client = Mock()
        expected_url = "https://test-bucket.s3.amazonaws.com/test-key?signature=abc123"
        mock_client.generate_presigned_url.return_value = expected_url
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket")

        result = storage.get_presigned_url("test-key", expiration=7200)

        mock_client.generate_presigned_url.assert_called_once_with(
            "get_object",
            Params={"Bucket": "test-bucket", "Key": "test-key"},
            ExpiresIn=7200
        )
        assert result == expected_url

    @patch('common.s3_storage.boto3.client')
    def test_get_presigned_url_default_expiration(self, mock_boto_client):
        """Test presigned URL generation with default expiration."""
        mock_client = Mock()
        mock_client.generate_presigned_url.return_value = "test-url"
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket")

        storage.get_presigned_url("test-key")

        mock_client.generate_presigned_url.assert_called_once_with(
            "get_object",
            Params={"Bucket": "test-bucket", "Key": "test-key"},
            ExpiresIn=3600  # Default value
        )

    @patch('common.s3_storage.boto3.client')
    def test_get_presigned_url_client_error(self, mock_boto_client):
        """Test presigned URL generation with client error."""
        mock_client = Mock()
        mock_client.generate_presigned_url.side_effect = ClientError(
            {"Error": {"Code": "AccessDenied", "Message": "Access denied"}},
            "GeneratePresignedUrl"
        )
        mock_boto_client.return_value = mock_client
        storage = S3Storage(bucket_name="test-bucket")

        result = storage.get_presigned_url("test-key")

        assert result is None


class TestGetS3Storage:
    """Test get_s3_storage function."""

    @patch('common.s3_storage.S3Storage')
    def test_get_s3_storage(self, mock_s3_storage_class):
        """Test get_s3_storage function."""
        mock_instance = Mock()
        mock_s3_storage_class.return_value = mock_instance

        result = get_s3_storage("custom-prefix")

        mock_s3_storage_class.assert_called_once_with(prefix="custom-prefix")
        assert result == mock_instance
